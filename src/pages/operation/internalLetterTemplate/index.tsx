import { useMutation } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { deleteMailTemplate, updateMailTemplateOrder } from '@/api/mail';
import OperatorCell from '@/components/cells/OperatorCell';
import DragTable from '@/components/DragTable';
import RButton from '@/components/RButton';
import useConfirmModal from '@/hooks/useConfirmModal';
import TableSearchLayout from '@/layout/TableSearchLayout';
import { MailTemplate } from '@/types/mail';

import useMailTemplates from './hooks/useMailTemplates';

const TableWrap = ({
  handleEdit,
  handleCopy,
  handleDelete,
  list,
  isLoadingList
}: {
  handleEdit: (record: MailTemplate) => void;
  handleCopy: (record: MailTemplate) => void;
  handleDelete: (record: MailTemplate) => void;
  list?: MailTemplate[];
  isLoadingList?: boolean;
}) => {
  const { t } = useTranslation();
  const [dataSource, setDataSource] = useState<MailTemplate[]>([]);

  useEffect(() => {
    setDataSource(list || []);
  }, [list]);

  const { mutate: updateMailTemplateOrderMutation, isPending: isUpdateOrderPending } = useMutation({
    mutationFn: updateMailTemplateOrder,
    onError: () => {
      setDataSource(list || []);
    }
  });

  const handleChangeOrder = (data: MailTemplate[]) => {
    setDataSource(data);
    updateMailTemplateOrderMutation({ orders: data.map((item) => item.id) });
  };

  const tableColumns = useMemo(
    () => [
      {
        title: t('internal_letter_template_id'),
        dataIndex: 'templateId',
        key: 'templateId'
      },
      {
        title: t('internal_letter_template_category'),
        dataIndex: 'categoryLabel',
        key: 'categoryLabel'
      },
      {
        title: t('internal_letter_template_type'),
        dataIndex: 'typeLabel',
        key: 'typeLabel'
      },

      {
        title: t('internal_letter_template_title'),
        dataIndex: 'title',
        key: 'title',
        width: 200,
        ellipsis: true
      },
      {
        title: t('common_lastOperate'),
        dataIndex: 'updatedAt',
        render: (_: number, record: MailTemplate) => {
          return (
            <OperatorCell record={{ updatedBy: record.createdBy, updatedAt: record.createdAt }} />
          );
        }
      },
      {
        title: t('common_note'),
        dataIndex: 'description',
        key: 'description',
        width: 200,
        ellipsis: true,
        render: (description: string) => {
          if (!description) return '-';
          return <div className="line-clamp-2">{description}</div>;
        }
      },
      {
        title: t('common_action'),
        dataIndex: 'action',
        render: (_: string, record: MailTemplate) => {
          return (
            <div className="flex gap-1">
              <RButton
                variant="outlined"
                color="primary"
                type="link"
                size="small"
                onClick={() => handleEdit(record)}
              >
                {t('common_edit')}
              </RButton>
              <RButton
                variant="outlined"
                color="green"
                type="link"
                size="small"
                onClick={() => handleCopy(record)}
              >
                {t('common_copy')}
              </RButton>
              <RButton
                variant="outlined"
                color="red"
                type="link"
                size="small"
                onClick={() => handleDelete(record)}
              >
                {t('common_delete')}
              </RButton>
            </div>
          );
        }
      }
    ],
    [handleCopy, handleDelete, handleEdit, t]
  );

  return (
    <DragTable
      keyName="id"
      onSortChange={handleChangeOrder}
      loading={isLoadingList || isUpdateOrderPending}
      rowKey="id"
      dataSource={dataSource}
      columns={tableColumns}
      pagination={false}
    />
  );
};

const InternalLetterTemplatePage = () => {
  const { t } = useTranslation();
  const { confirmModal } = useConfirmModal();
  const [open, setOpen] = useState(false);
  const [initialValues, setInitialValues] = useState<MailTemplate>();
  const mailTemplatesQuery = useMailTemplates({});
  const deleteMutation = useMutation({
    mutationFn: deleteMailTemplate,
    onSuccess: () => {
      mailTemplatesQuery.refetch();
    }
  });

  const handleAdd = () => {
    setOpen(true);
    setInitialValues(undefined);
  };

  const handleEdit = (record: MailTemplate) => {
    // setInitialValues(record);
    // setOpen(true);
  };

  const handleCopy = (record: MailTemplate) => {
    // setInitialValues({
    //   ...record,
    //   id: undefined
    // });
    // setOpen(true);
  };

  const handleDelete = (record: MailTemplate) => {
    confirmModal({
      content: t('common_confirm_delete_name', { name: record.title }),
      onOk: () => {
        deleteMutation.mutate({ id: record.id });
      }
    });
  };

  return (
    <>
      <TableSearchLayout>
        <RButton className="mb-4" onClick={handleAdd}>
          {t('common_add_name', { name: t('internal_letter_template') })}
        </RButton>
        <TableWrap
          handleEdit={handleEdit}
          handleCopy={handleCopy}
          handleDelete={handleDelete}
          list={mailTemplatesQuery.data?.data?.data}
          isLoadingList={mailTemplatesQuery.isPending}
        />
      </TableSearchLayout>
    </>
  );
};

export default InternalLetterTemplatePage;
