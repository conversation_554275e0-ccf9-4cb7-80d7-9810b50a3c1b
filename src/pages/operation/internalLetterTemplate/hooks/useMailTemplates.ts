import { useQuery } from '@tanstack/react-query';

import { getMailTemplateList } from '@/api/mail';
import { MailTemplateSearchParams } from '@/types/mail';

// Query keys
export const mailTemplateKeys = {
  all: ['mailTemplate'] as const,
  lists: () => [...mailTemplateKeys.all, 'list'] as const,
  list: (params: MailTemplateSearchParams) => [...mailTemplateKeys.lists(), params] as const,
  details: () => [...mailTemplateKeys.all, 'detail'] as const,
  detail: (id: number) => [...mailTemplateKeys.details(), id] as const
};

export const useMailTemplates = (params: MailTemplateSearchParams = {}) => {
  return useQuery({
    queryKey: mailTemplateKeys.list(params),
    queryFn: () => getMailTemplateList(params)
  });
};

export default useMailTemplates;
