import { useMutation, useQueryClient } from '@tanstack/react-query';

import {
  createMailTemplate,
  deleteMailTemplate,
  updateMailTemplate,
  updateMailTemplateOrder
} from '@/api/mail';

import { mailTemplateKeys } from './useMailTemplates';

export const useMailTemplateMutations = () => {
  const queryClient = useQueryClient();

  const invalidateQueries = () => {
    queryClient.invalidateQueries({ queryKey: mailTemplateKeys.all });
  };

  const createMutation = useMutation({
    mutationFn: createMailTemplate,
    onSuccess: invalidateQueries
  });

  const updateMutation = useMutation({
    mutationFn: updateMailTemplate,
    onSuccess: invalidateQueries
  });

  const deleteMutation = useMutation({
    mutationFn: deleteMailTemplate,
    onSuccess: invalidateQueries
  });

  const updateOrderMutation = useMutation({
    mutationFn: updateMailTemplateOrder,
    onSuccess: invalidateQueries
  });

  return {
    createMutation,
    updateMutation,
    deleteMutation,
    updateOrderMutation
  };
};

export default useMailTemplateMutations;
